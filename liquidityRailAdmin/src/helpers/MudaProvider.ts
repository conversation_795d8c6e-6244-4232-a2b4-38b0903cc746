import axios from "axios";
import crypto from 'crypto';
import BaseModel from "./base.model";
import { quoteRequest } from "../interfaces/quote.interfaces";
import { RateResponse } from "../interfaces/rate.interfaces";
export interface CreatePaymentIntentData {
    reference_id: string;
    amount: number|string
    from_currency: string;
    to_currency: string;
    payment_method: {
        address: string;
        asset_code: string;
        chain: string;
        memo: string;
    };
}
interface fiatResponse {
    transaction_id: string,
    status: "PENDING",
    send_amount: string,
    payment_type: "CARD" | "BANK" | "MOBILE_MONEY",
    payment_url: string

}
interface cryptoResponse {
    address: string,
    asset_code: string,
    "chain": "BSC" | "TRON" | "STELLAR",
    memo: string
}
export class MudaProvider extends BaseModel {
    private apiKey: string;
    constructor(apiKey: string) {
        super("providers");
        this.apiKey = apiKey;
    }

    async makeFiatResponse(statusCode: number, message: string, data: fiatResponse) {
        return {
            status: statusCode,
            message: message,
            data: data,
        };
    }
    async makeCryptoResponse(statusCode: number, message: string, data: cryptoResponse) {
        return {
            status: statusCode,
            message: message,
            data: data,
        };
    }
    async createPaymentIntent(providerId: string, data: CreatePaymentIntentData) {
        const baseUrl = await this.getProviderBaseUrl(providerId);
        const postUrl = `${baseUrl}/create-payment-intent`;
        console.log("postUrl", postUrl);
        console.log("data", data);
        const response = await this.postRequest(postUrl, data);
        console.log("response", response);
        return response;
    }

    async getTransaction(providerId: string, transId: string) {
        const baseUrl = await this.getProviderBaseUrl(providerId);
        const postUrl = `${baseUrl}/get-lr-transaction/${transId}`;
        console.log("postUrl", postUrl);
         const response = await this.getRequest(postUrl);
        console.log("response", response);
        return response;
    }

    async sendwebhook(providerId: string, data: string, eventType: string='general') {
        const baseUrl = await this.getProviderBaseUrl(providerId);
        const postUrl = `${baseUrl}/muda-events`;
        console.log("postUrl", postUrl);
        console.log("data", data);
        const response = await this.postRequest(postUrl, data, eventType);
        console.log("response", response);
        return response;
    }
    

    async getQuote(providerId: any, data: quoteRequest): Promise<RateResponse | null> {
        const baseUrl = await this.getProviderBaseUrl(providerId);
        const postUrl = `${baseUrl}/generate-lr-quote`;
        console.log("postUrl", postUrl);
        console.log("data", data);
        const response = await this.postRequest(postUrl, data);
        console.log("MDX_PROVIDER_RESPONSE", response);

        if (response.status == 200) {
            const formatedResponse: RateResponse = {
                provider: "hidden",
                providerQuoteId: response.data.quote_id,
                from: data.currency,
                providerId: providerId,
                to: data.asset_code,
                fiatAmount: response.data.total_amount,
                toAmount: response.data.rate,
                cryptoAmount: response.data.rate,
                fee: response.data.fee,
                quoteId: response.data.quote_id,
                expiresAt: response.data.expires_at,
                quotedPrice: response.data.rate
            }
            return formatedResponse;
        } else {
            return null;
        }

    }

    async postRequest(postUrl: string, payload: any, eventType: string='general') {
        const headers = this.getAuthHeaders(payload, eventType);
        console.log("Request Headers", headers);
        const response = await axios.post(postUrl, payload, { headers });
        try {
            return response.data;
        } catch (error) {
            console.log("error", error);
            return error;
        }
    }

    async getRequest(getUrl: string) {
        const headers = this.getAuthHeaders("", "general");
        const response = await axios.get(getUrl, { headers });
        return response.data;
    }


    getHmacSignature(rawBody: string, timestamp: string) {
        const signingKey = "CG7L5MOIT2ZH4XJXSVGOPXFOB42OWQ3NRNSVKJVS4223SVPXR2XGE5E"
        return crypto.createHmac("sha256", signingKey).update(rawBody+timestamp).digest("hex");
    }

    getAuthHeaders(payload: any, eventType: string) {
        const rawBody = typeof payload === "string" ? payload : JSON.stringify(payload); // use the SAME string for body + signature
        const timestamp = Math.floor(Date.now() / 1000); // unix seconds
        const signature = this.getHmacSignature(rawBody, timestamp.toString());

        return {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-Webhook-Timestamp": timestamp.toString(),
            "X-Webhook-Signature": signature,
            "X-Muda-Event-Type": eventType,
            "Authorization": `Bearer ${this.apiKey}`
        };
    }


    async getProviderBaseUrl(providerId: string) {
        const provider: any = await this.callQuerySafe("SELECT * FROM providers WHERE provider_id = ?", [providerId]);
        if (provider.length == 0) {
            return null;
        }
        return provider[0].base_url;
    }


}