import {
    CryptoReceivedWebhook,
    CryptoSentWebhook,
    FiatReceivedWebhook,
    FiatSentWebhook,
    GeneralStatusWebhook,
    LRProviderEvents,
    WebhookPayload
} from '../interfaces/webhook.interfaces';

export class WebhookFactory {

    static createCryptoReceived(data: any, provider: 'moralis' | 'fireblocks' | 'quidax' | 'stellar'): CryptoReceivedWebhook {

        if (provider === 'quidax') {
            const currency = data.payment_address.currency.toUpperCase() || ''
            const asset_code = data.payment_address.currency.toUpperCase() || ''
            return {
                eventType: 'crypto_received',
                provider: 'quidax',
                transactionId: data.id || data.txid,
                status: data.status === 'accepted' ? 'SUCCESSFUL' : 'PENDING',
                amount: data.amount || '0',
                hash: data.txid || '',
                from_address: '',
                to_address: data.payment_address.address,
                asset_code: asset_code,
                contract_address: asset_code,
                fee: data.fee,
                currency: currency
            };
        } else if (provider === 'moralis') {
            return {
                eventType: 'crypto_received',
                provider: 'moralis',
                transactionId: data.txs?.[0]?.hash || 'moralis_tx',
                status: data.txs?.[0]?.receiptStatus === "1" ? 'SUCCESSFUL' : 'PENDING',
                amount: data.erc20Transfers?.[0]?.valueWithDecimals || '0',
                hash: data.txs?.[0]?.hash || '',
                from_address: data.erc20Transfers?.[0]?.from || '',
                to_address: data.erc20Transfers?.[0]?.to || '',
                asset_code: data.erc20Transfers?.[0]?.tokenSymbol || '',
                contract_address: data.erc20Transfers?.[0]?.contract,
                fee: data.txs?.[0]?.receiptGasUsed,
                currency: data.erc20Transfers?.[0]?.tokenSymbol
            };
        } else if (provider === 'stellar') {
              return {
                eventType: 'crypto_received',
                provider: 'stellar',
                transactionId: data.id,
                status: 'SUCCESSFUL',
                amount: data.amount,
                hash: data.transaction_hash,
                from_address: data.from,
                to_address: data.to,
                asset_code: data.asset_code,
                contract_address: data.asset_code,
                fee: data.fee,
                memo: data.memo,
                currency: data.asset_code
            }

        } else { // fireblocks
            return {
                eventType: 'crypto_received',
                provider: 'fireblocks',
                transactionId: data.id,
                status: data.status,
                amount: data.amount,
                hash: data.txHash,
                from_address: data.sourceAddress || '',
                to_address: data.destinationAddress || '',
                asset_code: data.assetId || '',
                currency: data.assetId,
                memo: data.memo || ''
            };
        }
    }

    static createCryptoSent(data: any, provider: 'fireblocks' | 'quidax'): CryptoSentWebhook {
        return {
            eventType: 'crypto_sent',
            provider,
            transactionId: data.id || data.txHash,
            status: data.status,
            amount: data.amount,
            hash: data.txHash || data.hash,
            to_address: data.destinationAddress || data.to_address,
            asset_code: data.assetId || data.asset_code,
            fee: data.fee
        };
    }

    static createFiatReceived(data: any, provider: 'quidax' | 'kotani'): FiatReceivedWebhook {
        return {
            eventType: 'fiat_received',
            provider,
            transactionId: data.id || data.transaction_id,
            status: data.status || 'received',
            amount: data.amount,
            currency: data.currency,
            deposit_address: data.wallet?.deposit_address || data.deposit_address,
            account_number: data.account_number
        };
    }

    static createFiatSent(data: any, provider: 'muda' | 'ogateway' | 'kotani'): FiatSentWebhook {
        return {
            eventType: 'fiat_sent',
            provider,
            transactionId: data.transaction_id || data.id,
            status: data.status.toLowerCase().contains('success') ? 'SUCCESSFUL' : 'FAILED',
            amount: data.amount,
            currency: data.currency,
            account_number: data.account_number,
            reference_id: data.reference_id
        };
    }

    static createGeneralStatus(data: any): GeneralStatusWebhook {
        return {
            eventType: 'status_update',
            provider: 'general',
            transactionId: data.transactionId || data.id,
            status: data.status,
            reference_id: data.reference_id,
            hash: data.hash,
            amount: data.amount,
            currency: data.currency
        };
    }

    // Helper method to determine webhook type from provider and event
    static createWebhookFromProvider(data: any, provider: string, event?: string): WebhookPayload {
        switch (provider) {
            case 'moralis':
                return this.createCryptoReceived(data, 'moralis');

            case 'fireblocks':
                if (event === 'TRANSACTION_STATUS_UPDATED') {
                    return this.createCryptoReceived(data, 'fireblocks');
                }
                break;

            case 'quidax':
                if (event === 'deposit.successful') {
                    return this.createFiatReceived(data, 'quidax');
                }
                break;

            case 'muda':
                return this.createFiatSent(data, 'muda');

            case 'ogateway':
                return this.createFiatSent(data, 'ogateway');

            case 'kotani':
                // Could be either fiat received or sent, check the data
                if (data.type === 'deposit' || data.event === 'deposit') {
                    return this.createFiatReceived(data, 'kotani');
                } else {
                    return this.createFiatSent(data, 'kotani');
                }

            default:
                return this.createGeneralStatus(data);
        }

        // Fallback to general status
        return this.createGeneralStatus(data);
    }

    static createLRProviderEvents(data: any): LRProviderEvents {
     return data as LRProviderEvents
    }
} 