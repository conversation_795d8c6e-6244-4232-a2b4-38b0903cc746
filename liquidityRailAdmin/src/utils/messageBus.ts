/**
 * Simple Message Bus Utility for Liquidity Rail Admin
 * Handles RabbitMQ message queuing for basic transaction events
 */

import amqp, { Channel, Connection, Message, ChannelModel } from 'amqplib';
import { EventEmitter } from 'events';

// Simple transaction event interface
export interface TransactionEvent {
  transactionId: string;
  status: 'pending' | 'completed' | 'failed';
  amount: number;
  timestamp: string;
}

// Message bus configuration
export interface MessageBusConfig {
  host: string;
  port: number;
  username: string;
  password: string;
}

/**
 * Simple Message Bus class for handling RabbitMQ operations
 */
export class MessageBus extends EventEmitter {
  private connection: ChannelModel | null = null;
  private channel: Channel | null = null;
  private config: MessageBusConfig;
  private isConnected: boolean = false;

  // Simple queue name
  private readonly queueName = 'transactions';

  constructor(config: MessageBusConfig) {
    super();
    this.config = config;
  }

  /**
   * Connect to RabbitMQ
   */
  async connect(): Promise<void> {
    try {
      const url = `amqp://${this.config.username}:${this.config.password}@${this.config.host}:${this.config.port}`;
      
      this.connection = await amqp.connect(url);
      this.channel = await this.connection.createChannel();
      
      // Declare simple queue
      await this.channel.assertQueue(this.queueName, { durable: true });
      
      this.isConnected = true;
      this.emit('connected');
      
      console.log(`Connected to RabbitMQ at ${this.config.host}:${this.config.port}`);
      
    } catch (error) {
      console.error('Failed to connect to RabbitMQ:', error);
      this.emit('connection_error', error);
      throw error;
    }
  }

  /**
   * Publish transaction event
   */
  async publishTransaction(event: TransactionEvent): Promise<boolean> {
    if (!this.channel || !this.isConnected) {
      throw new Error('Not connected to RabbitMQ');
    }

    try {
      const message = Buffer.from(JSON.stringify(event));
      
      const success = this.channel.sendToQueue(this.queueName, message, {
        persistent: true
      });

      if (success) {
        console.log(`Published transaction event: ${event.transactionId}`);
        this.emit('event_published', event);
        return true;
      } else {
        console.error('Failed to publish event');
        return false;
      }

    } catch (error) {
      console.error('Failed to publish transaction event:', error);
      this.emit('publish_error', error, event);
      return false;
    }
  }

  /**
   * Consume messages from queue
   */
  async consumeMessages(callback: (msg: Message) => void): Promise<void> {
    if (!this.channel || !this.isConnected) {
      throw new Error('Not connected to RabbitMQ');
    }

    try {
      await this.channel.consume(this.queueName, (msg: Message | null) => {
        if (msg) {
          try {
            callback(msg);
          } catch (error) {
            console.error('Error processing message:', error);
            this.channel?.nack(msg, false, true);
          }
        }
      });

      console.log(`Started consuming from queue: ${this.queueName}`);
      
    } catch (error) {
      console.error(`Failed to start consuming:`, error);
      throw error;
    }
  }

  /**
   * Acknowledge a message
   */
  ackMessage(msg: Message): void {
    if (this.channel) {
      this.channel.ack(msg);
    }
  }

  /**
   * Reject a message
   */
  nackMessage(msg: Message, requeue: boolean = false): void {
    if (this.channel) {
      this.channel.nack(msg, false, requeue);
    }
  }

  /**
   * Check connection health
   */
  isHealthy(): boolean {
    return this.isConnected && 
           this.connection !== null && 
           this.channel !== null;
  }

  /**
   * Close connection
   */
  async close(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }

      if (this.connection) {
        await this.connection.close();
        this.connection = null;
      }

      this.isConnected = false;
      this.emit('disconnected');
      console.log('RabbitMQ connection closed');
      
    } catch (error) {
      console.error('Error closing RabbitMQ connection:', error);
      throw error;
    }
  }
}

// Simple utility function to create transaction events
export function createTransactionEvent(
  transactionId: string,
  status: 'pending' | 'completed' | 'failed',
  amount: number
): TransactionEvent {
  return {
    transactionId,
    status,
    amount,
    timestamp: new Date().toISOString()
  };
}

// Export default MessageBus class
export default MessageBus;
