import RequestHelper from "../helpers/request.helper";
import axios, { AxiosResponse } from "axios";
import dotenv from "dotenv";

dotenv.config();

// Types and interfaces
interface MagmaPayConfig {
  apiUrl: string;
  publicKey: string;
  apiKey: string;
}

interface TokenResponse {
  status: "success" | "error";
  token?: string;
  message?: string;
}

class MagmaPay {
  private config: MagmaPayConfig;
  private requestHeaders: Record<string, string>;

  constructor() {
    this.config = {
      apiUrl: process.env.MAGMAPAY_API_URL ?? "https://api.magmaonepay.com",
      publicKey: process.env.MAGMAPAY_PUBLIC_KEY ?? "",
      apiKey: process.env.MAGMAPAY_API_KEY ?? "",
    };

    this.requestHeaders = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };
  }

  // Generate authentication token (if required by MagmaOnePay)
  private async generateToken(): Promise<TokenResponse> {
    try {
      const endpoint = "/auth/generate-bearer-token";
      const data = {
        publicKey: this.config.publicKey,
        "api-key": this.config.apiKey,
      };

      axios.defaults.headers.common["api-key"] = this.config.apiKey;

      const response: AxiosResponse = await axios.post(
        `${this.config.apiUrl}${endpoint}`,
        data
      );

      if (response?.data?.success) {
        return {
          status: "success",
          token: response.data.token,
        };
      }

      return {
        status: "error",
        message: response?.data?.message || "Failed to generate token",
      };
    } catch (error: any) {
      console.error("Token generation error:", error);
      return {
        status: "error",
        message: error?.message ?? "Error generating token",
      };
    }
  }

  // General request handler
  private async makeRequest(
    method: "get" | "post" | "put",
    endpoint: string,
    data: any = {}
  ): Promise<any> {
    try {
      const fullUrl = `${this.config.apiUrl}${endpoint}`.replace(/\s/g, "");

      await RequestHelper.setEndpoint(fullUrl);
      const tokenResponse = await this.generateToken();

      if (tokenResponse.status !== "success" || !tokenResponse.token) {
        throw new Error("Failed to generate MagmaOnePay token");
      }

      this.requestHeaders["Authorization"] = `Bearer ${tokenResponse.token}`;
      await RequestHelper.setHeaders(this.requestHeaders);

      if (data && Object.keys(data).length > 0) {
        await RequestHelper.setData(data);
      }

      await RequestHelper[`${method}Request`]();
      const errors = await RequestHelper.getErrors();
      const responseData = await RequestHelper.getResults();
      return errors?.status ? responseData : responseData;
    } catch (error: any) {
      console.error(`Request error for ${method.toUpperCase()} ${endpoint}:`, error);
      throw new Error(error.message || "Request failed");
    }
  }

  // 🔹 Collection (Payin)
  public async initiatePayment(data: {
    amount: number;
    currency: string;
    paymentMethod: string;
    customer: { name: string; email: string; phone?: string };
    reference: string;
    redirectUrl?: string;
    callbackUrl?: string;
  }) {
    return this.makeRequest("post", "/payments/initiate", data);
  }

  public async validatePayment(paymentId: string, data: { code: string }) {
    return this.makeRequest("post", `/payments/${paymentId}/validate`, data);
  }

  public async getPaymentStatus(paymentId: string) {
    return this.makeRequest("get", `/payments/${paymentId}/status`);
  }

  // 🔹 Payout
  public async initiatePayout(data: {
    amount: number;
    currency: string;
    payoutMethod: string;
    beneficiary: {
      name: string;
      accountNumber: string;
      bankCode?: string;
      walletAddress?: string;
    };
    reference: string;
    callbackUrl?: string;
  }) {
    return this.makeRequest("post", "/payouts/initiate", data);
  }

  public async getPayoutStatus(payoutId: string) {
    return this.makeRequest("get", `/payouts/${payoutId}/status`);
  }

  // 🔹 Unique transaction reference
  public generateUniqueReference(): string {
    return `MAGMAPAY_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 10)}`;
  }
}

export default MagmaPay;

