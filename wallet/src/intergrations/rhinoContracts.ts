import { ethers, parseUnits } from 'ethers';
import dotenv from 'dotenv';

dotenv.config();

// Types for contract interaction
export interface ChainConfig {
  rpc: string;
  contractAddress: string;
  nativeTokenName: string;
  tokens: Record<string, {
    token: string;
    address: string;
    decimals: number;
  }>;
}

export interface BridgeContractCallProps {
  chainConfig: ChainConfig;
  amount: string;
  token: string;
  commitmentId: string;
  chainName: string; // Added to determine which private key to use
  callback?: (txHash: string) => void;
}

export interface SwapContractCallProps extends BridgeContractCallProps {
  tokenIn: string;
  tokenOut: string;
}

export enum ChainType {
  EVM = 'evm'
}



// DVF Deposit Contract ABI (copied exactly from bridge-examples)
const DVF_DEPOSIT_CONTRACT_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "token", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"},
      {"internalType": "uint256", "name": "id", "type": "uint256"}
    ],
    "name": "depositWithId",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "id", "type": "uint256"}
    ],
    "name": "depositNativeWithId",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  }
];

// ERC20 ABI (copied exactly from bridge-examples)
const ERC20_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "spender", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "approve",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "address", "name": "spender", "type": "address"}
    ],
    "name": "allowance",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "account", "type": "address"}
    ],
    "name": "balanceOf",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "decimals",
    "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}],
    "stateMutability": "view",
    "type": "function"
  }
];

/**
 * Get EVM token allowance for bridge contract
 */
export async function getEVMTokenAllowance(
  chainConfig: ChainConfig,
  token: string,
  address: string
): Promise<number> {
  try {
    const provider = new ethers.JsonRpcProvider(chainConfig.rpc);
    const tokenConfig = chainConfig.tokens[token];
    
    if (!tokenConfig) {
      throw new Error(`Token ${token} not found in chain config`);
    }

    const tokenContract = new ethers.Contract(tokenConfig.address, ERC20_ABI, provider);
    const allowance = await tokenContract.allowance(address, chainConfig.contractAddress);
    
    return parseFloat(ethers.formatUnits(allowance, tokenConfig.decimals));
  } catch (error: any) {
    console.error('Error getting token allowance:', error);
    return 0;
  }
}

/**
 * Execute EVM bridge contract call (copied exactly from bridge-examples/examples/contracts/evmBridge.ts)
 */
export async function callEvmBridgeContract(
  props: BridgeContractCallProps
): Promise<{ success: boolean; txHash?: string; receipt?: any; error?: string }> {
  const { chainConfig, amount, token, commitmentId, chainName, callback } = props;

  // Get private key from environment variables
  const privateKey = getPrivateKeyForChain(chainName);

  try {
    console.log('Rhino: Executing EVM bridge contract call', {
      contractAddress: chainConfig.contractAddress,
      token,
      amount,
      commitmentId: commitmentId.substring(0, 8) + '...'
    });

    const wallet = new ethers.Wallet(privateKey, new ethers.JsonRpcProvider(chainConfig.rpc));
    const tokenConfig = chainConfig.tokens[token];

    if (!tokenConfig) {
      throw new Error(`Token ${token} not supported on this chain`);
    }

    const amountWithDecimals = +amount * 10 ** tokenConfig.decimals;
    const tokenAddress = tokenConfig.address;
    const bridgeContractAddress = chainConfig.contractAddress;
    const isNativeToken = chainConfig.nativeTokenName === token;

    const depositContract = new ethers.Contract(bridgeContractAddress, DVF_DEPOSIT_CONTRACT_ABI, wallet);

    // For ERC20 tokens we call 'depositWithId' and for native tokens we call 'depositNativeWithId'
    if (!isNativeToken) {
      console.log('Rhino: Handling ERC20 token deposit for', token);

      const allowance = await getEVMTokenAllowance(chainConfig, token, wallet.address);
      console.log('Rhino: Current allowance:', allowance, 'Required:', amount);

      if (allowance === 0 || allowance < parseFloat(amount)) {
        console.log('Rhino: Approving token spend...');
        const erc20Contract = new ethers.Contract(tokenAddress, ERC20_ABI, wallet);
        const approveTx = await erc20Contract.approve(bridgeContractAddress, amountWithDecimals);
        await approveTx.wait();
        console.log('Rhino: Token approval confirmed');
      }

      console.log('Rhino: Executing depositWithId...');
      const tx = await depositContract.depositWithId(tokenAddress, amountWithDecimals, BigInt(`0x${commitmentId}`));
      callback?.(tx.hash);
      const receipt = await tx.wait();

      console.log('Rhino: ERC20 bridge transaction confirmed:', tx.hash);
      return { success: true, txHash: tx.hash, receipt };
    }

    console.log('Rhino: Handling native token deposit for', token);
    const tx = await depositContract.depositNativeWithId(BigInt(`0x${commitmentId}`), {
      value: parseUnits(amount, 'ether'),
    });
    callback?.(tx.hash);
    const receipt = await tx.wait();

    console.log('Rhino: Native bridge transaction confirmed:', tx.hash);
    return { success: true, txHash: tx.hash, receipt };

  } catch (error: any) {
    console.error('Rhino: EVM bridge contract call failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Execute bridge-swap contract call (uses same contract as bridge)
 */
export async function callEvmBridgeSwapContract(
  props: SwapContractCallProps
): Promise<{ success: boolean; txHash?: string; receipt?: any; error?: string }> {
  // Bridge-swap uses the same contract as bridge, just with different tokens
  const bridgeProps: BridgeContractCallProps = {
    chainConfig: props.chainConfig,
    amount: props.amount,
    token: props.tokenIn, // Use input token for the deposit
    commitmentId: props.commitmentId,
    chainName: props.chainName,
    callback: props.callback
  };

  return callEvmBridgeContract(bridgeProps);
}

/**
 * Get wallet address from private key
 */
export function getWalletAddress(privateKey: string): string {
  const wallet = new ethers.Wallet(privateKey);
  return wallet.address;
}

/**
 * Get private key from environment variables based on chain name
 * For EVM chains, use a single private key. For other chains, use chain-specific keys.
 */
function getPrivateKeyForChain(chainName: string): string {
  // For all EVM chains, use the same private key
  const evmChains = [
    'ethereum', 'polygon', 'arbitrum', 'optimism', 'base', 'bsc',
    'avalanche', 'fantom', 'gnosis', 'celo', 'moonbeam', 'moonriver',
    'harmony', 'cronos', 'aurora', 'metis', 'boba', 'kava', 'klaytn'
  ];

  if (evmChains.includes(chainName.toLowerCase())) {
    const evmPrivateKey = process.env.EVM_PRIVATE_KEY;
    if (!evmPrivateKey) {
      throw new Error('EVM_PRIVATE_KEY environment variable is required for EVM chains');
    }
    return evmPrivateKey;
  }

  // For future non-EVM chains, use chain-specific environment variables
  const chainPrivateKey = process.env[`${chainName.toUpperCase()}_PRIVATE_KEY`];
  if (!chainPrivateKey) {
    throw new Error(`${chainName.toUpperCase()}_PRIVATE_KEY environment variable is required for ${chainName}`);
  }

  return chainPrivateKey;
}

/**
 * Validate private key format
 */
export function validatePrivateKey(privateKey: string): boolean {
  try {
    new ethers.Wallet(privateKey);
    return true;
  } catch {
    return false;
  }
}




