import { ChainConfig } from './rhinoContracts';

// Chain configurations copied from bridge-examples/config/chainConfigs.ts
export const CHAIN_CONFIGS: Record<string, ChainConfig> = {
  ethereum: {
    rpc: process.env.ETHEREUM_RPC || 'https://eth.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'ETH',
    tokens: {
      ETH: {
        token: 'ETH',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 6,
      },
      USDT: {
        token: 'USDT',
        address: '******************************************',
        decimals: 6,
      },
      WBTC: {
        token: 'WBTC',
        address: '******************************************',
        decimals: 8,
      },
    },
  },
  polygon: {
    rpc: process.env.POLYGON_RPC || 'https://polygon.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'MATIC',
    tokens: {
      MATIC: {
        token: 'MATIC',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 6,
      },
      USDT: {
        token: 'USDT',
        address: '******************************************',
        decimals: 6,
      },
      WBTC: {
        token: 'WBTC',
        address: '******************************************',
        decimals: 8,
      },
    },
  },
  arbitrum: {
    rpc: process.env.ARBITRUM_RPC || 'https://arbitrum.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'ETH',
    tokens: {
      ETH: {
        token: 'ETH',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 6,
      },
      USDT: {
        token: 'USDT',
        address: '******************************************',
        decimals: 6,
      },
      WBTC: {
        token: 'WBTC',
        address: '******************************************',
        decimals: 8,
      },
    },
  },
  optimism: {
    rpc: process.env.OPTIMISM_RPC || 'https://optimism.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'ETH',
    tokens: {
      ETH: {
        token: 'ETH',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 6,
      },
      USDT: {
        token: 'USDT',
        address: '******************************************',
        decimals: 6,
      },
      WBTC: {
        token: 'WBTC',
        address: '******************************************',
        decimals: 8,
      },
    },
  },
  base: {
    rpc: process.env.BASE_RPC || 'https://base.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'ETH',
    tokens: {
      ETH: {
        token: 'ETH',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 6,
      },
    },
  },
  bsc: {
    rpc: process.env.BSC_RPC || 'https://bsc.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'BNB',
    tokens: {
      BNB: {
        token: 'BNB',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 18,
      },
      USDT: {
        token: 'USDT',
        address: '******************************************',
        decimals: 18,
      },
      BTCB: {
        token: 'BTCB',
        address: '******************************************',
        decimals: 18,
      },
    },
  },
  avalanche: {
    rpc: process.env.AVALANCHE_RPC || 'https://avalanche.llamarpc.com',
    contractAddress: '******************************************',
    nativeTokenName: 'AVAX',
    tokens: {
      AVAX: {
        token: 'AVAX',
        address: '******************************************',
        decimals: 18,
      },
      USDC: {
        token: 'USDC',
        address: '******************************************',
        decimals: 6,
      },
      USDT: {
        token: 'USDT',
        address: '******************************************',
        decimals: 6,
      },
      WBTC: {
        token: 'WBTC',
        address: '******************************************',
        decimals: 8,
      },
    },
  },
};

/**
 * Get chain configuration by name
 */
export function getChainConfig(chainName: string): ChainConfig | null {
  const config = CHAIN_CONFIGS[chainName.toLowerCase()];
  return config || null;
}

/**
 * Get all supported chains
 */
export function getSupportedChains(): string[] {
  return Object.keys(CHAIN_CONFIGS);
}

/**
 * Get supported tokens for a chain
 */
export function getSupportedTokens(chainName: string): string[] {
  const config = getChainConfig(chainName);
  return config ? Object.keys(config.tokens) : [];
}

/**
 * Validate if a token is supported on a chain
 */
export function isTokenSupported(chainName: string, tokenName: string): boolean {
  const config = getChainConfig(chainName);
  return config ? tokenName in config.tokens : false;
}

/**
 * Get private key for a specific chain from environment variables
 */
export function getPrivateKeyForChain(chainName: string): string | null {
  const envKey = `${chainName.toUpperCase()}_PRIVATE_KEY`;
  return process.env[envKey] || process.env.RHINO_PRIVATE_KEY || null;
}

/**
 * Validate that all required environment variables are set for a chain
 */
export function validateChainEnvironment(chainName: string): { valid: boolean; missing: string[] } {
  const missing: string[] = [];
  
  // Check for private key
  const privateKey = getPrivateKeyForChain(chainName);
  if (!privateKey) {
    missing.push(`${chainName.toUpperCase()}_PRIVATE_KEY or RHINO_PRIVATE_KEY`);
  }
  
  // Check for RPC URL (optional, has defaults)
  const rpcEnvKey = `${chainName.toUpperCase()}_RPC`;
  if (!process.env[rpcEnvKey]) {
    console.warn(`Warning: ${rpcEnvKey} not set, using default RPC`);
  }
  
  return {
    valid: missing.length === 0,
    missing
  };
}
