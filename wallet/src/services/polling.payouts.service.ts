import Transactions from "../models/transactions";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import PegaPay from "../intergrations/PegPay";
import MyFX from "../intergrations/MyFX";
import { Steps } from "../helpers/model";
import Honey<PERSON>oin from "../intergrations/HoneyCoin";
import Tembo from "../intergrations/Tembo";
import { StatusCodes } from "../intergrations/interfaces";
import { getItem, setItem } from "../helpers/connectRedis";
import Model from "../helpers/model";
import { get } from "../helpers/httpRequest";

// Interface for polling response format
interface PollingResponse {
    statusCode: number;    // 200, 400, 202, 203
    transStatus: string;   // "SUCCESS", "FAILED", "PENDING", "ONHOLD"
    description: string;   // Human-readable description
    data: any;            // Original response data
}


const thirdPartyHandler = new ThirdPartyHandler();
const pg = new PegaPay();

class PollingPayoutsService extends Model {

    public async checkPendingDirectPayouts() {

        const pendingTransactions: any = await this.callRawQuery(
            "SELECT * FROM transactions WHERE (status = 'PENDING' OR status = 'PROCESSING') AND trans_type='PUSH' AND created_at <= DATE_SUB(NOW(), INTERVAL 1 MINUTE)"
        );
        for (const transaction of pendingTransactions) {
            const result = await new Transactions().handlePendingPayoutTransaction(transaction);
        }

        console.log(`Found ${pendingTransactions.length} pending direct payouts to check`);
    }



        public async getPagasusTransaction(transaction: any): Promise<PollingResponse> {
        const trans_id = transaction.trans_id;
         const amount = transaction.amount;
        const pgStatus: any = await new PegaPay().getTransactionDetails(trans_id, "PUSH");
        console.log(`pullRequest`, pgStatus)
        let statusCode = 0
        let description = ""
        let transStatus = ""

        description = await this.mapDescription(pgStatus.description)
        if (pgStatus && typeof pgStatus === 'object' && 'statusCode' in pgStatus) {
            let pgStatusCode = pgStatus.statusCode
            transStatus = this.checkTransactionStatus(pgStatusCode);

            if (pgStatusCode == "0" && description === "SUCCESS") {
                transStatus = "SUCCESS"
                statusCode = 200
                await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", pgStatus)
                console.log(`Transaction ${trans_id} completed successfully`);
            } else if (transStatus === "FAILED") {
                statusCode = 400
                await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", pgStatus)
            } else if (transStatus === "PENDING") {
                statusCode = 202
            } else if (transStatus === "ONHOLD" || transStatus === "RETRY") {
                statusCode = 203
                await new Model().saveTransactionLog(trans_id, "ONHOLD", Steps.TRANS_STATUS_CHECK, 203, "Transaction on hold", pgStatus)
            }
        }
        return {
            statusCode: statusCode,
            transStatus: transStatus,
            description: description,
            data: pgStatus

        }
    }



    public async getHoneyCoinTransactionStatus(transaction: any): Promise<PollingResponse> {
        const trans_id = transaction.trans_id;
        const client_id = transaction.client_id;
        const transactionDetails: any = await this.getHoneyCoinTransaction(trans_id);
        let statusCode = 0
        let description = ""
        let transStatus = ""

        console.log(`Honey coin payout transactionDetails:`, transactionDetails)
        if (transactionDetails) {
            statusCode = transactionDetails.statusCode
            description = transactionDetails.description
            transStatus = transactionDetails.transStatus

            if (statusCode == 200 && description === "SUCCESS") {
                transStatus = "SUCCESS"
                await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", transactionDetails?.data)
                console.log(`Transaction ${trans_id} completed successfully`);
            } else if (statusCode === 400 && description === "FAILED") {
                transStatus = "FAILED"
                await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", transactionDetails?.data)
            } else if (statusCode === 202) {
                transStatus = "PENDING"
            } else if (transStatus === "ONHOLD" || transStatus === "RETRY") {
                await new Model().saveTransactionLog(trans_id, "ONHOLD", Steps.TRANS_STATUS_CHECK, 203, "Transaction on hold", transactionDetails?.data)
            }
        }

        return {
            statusCode: statusCode,
            transStatus: transStatus,
            description: description,
            data: transactionDetails
        }
    }




 
    async checkLiquidityRailPayout(trans_id: any): Promise<PollingResponse> {
        const transinfo = await get(process.env.LIQUIDITY_RAIL_API_URL + `/accounts/transactions/${trans_id}`)
        console.log(`checkLiquidityRailPayout`, trans_id)
        const transaction = transinfo.data
        const status = transaction.status

        let statusCode = 0
        let description = ""
        let transStatus = ""

        if (status === "SUCCESSFUL") {
            statusCode = 200
            description = "Transaction completed successfully"
            transStatus = "SUCCESS"
            await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", transaction)
        } else if (status === "FAILED" || status === "EXPIRED") {
            statusCode = 400
            description = "Transaction failed"
            transStatus = "FAILED"
            await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", transaction)
        } else if (status === "PENDING") {
            statusCode = 202
            description = "Transaction pending"
            transStatus = "PENDING"
        }

        return {
            statusCode: statusCode,
            transStatus: transStatus,
            description: description,
            data: transaction
        }
    }

    async getHoneyCoinTransaction(transactionId: string) {
        try {

            const honeycoin = new HoneyCoin();
            const transaction = await honeycoin.getTransaction(transactionId);
            console.log("🔹 HoneyCoin transaction:", transaction)
            if (transaction?.data?.status === "SUCCESSFUL") {
                return this.mapPayoutResponse(200, "SUCCESS", transaction.data)
            } else if (transaction?.data?.status === "FAILED") {
                return this.mapPayoutResponse(400, transaction.data.note || "FAILED", transaction.data)
            } else {
                return this.mapPayoutResponse(202, transaction?.message || "Transaction pending")
            }
        } catch (error: any) {
            console.error("Error getting HoneyCoin transaction:", error);
            return false;
        }
    }

    /**
     * Check Tembo payout transaction status (Mobile Money)
     * This provides the missing polling logic for TZS_PAYOUT_MOBILE_MONEY
     */
    async checkTemboPayoutStatus(transactionId: string): Promise<PollingResponse> {
        try {
            console.log("🔹 Checking Tembo payout status for:", transactionId);

            // Get the Tembo transaction ID from the database
            const transactionQuery = `SELECT ext_reference FROM transactions WHERE trans_id = ?`;
            const transactionResult = await this.callQuerySafe(transactionQuery, [transactionId]) as any[];

            if (!transactionResult || transactionResult.length === 0) {
                console.log("❌ Transaction not found in database");
                return this.mapPayoutResponse(500, "Transaction not found in database");
            }

            const temboTransactionId = transactionResult[0]?.ext_reference;

            // If no Tembo transaction ID, return error - don't attempt status check
            if (!temboTransactionId) {
                console.log("❌ No Tembo transaction ID found - cannot check status");
                console.log("❌ Webhook may not have arrived yet or initial request failed");
                return this.mapPayoutResponse(500, "No Tembo transaction ID available - cannot check status");
            }

            // We have Tembo transaction ID - proceed with status check
            console.log("✅ Tembo transaction ID available - proceeding with status check");
            const tembo = new Tembo();
            const response = await tembo.getPaymentStatus(transactionId, temboTransactionId);
            console.log("🔹 Tembo payout status response:", response);

            let statusCode = 202; // Default to pending
            let description = "PENDING";

            if (response.status === 200 && response.data) {
                const temboStatus = response.data.statusCode || response.data.status;

                // Map Tembo status codes to our internal status - EXACT MATCH TO TEMBO DOCS
                if (temboStatus === 'PAYMENT_SUCCESSFUL' || temboStatus === 'SUCCESSFUL') {
                    statusCode = 200;
                    description = "Payment successful";
                    this.saveTransactionLog(transactionId, "SUCCESS", Steps.THIRDPARTY_RESPONSE, 200, "Payout successful", response.data);

                } else if (temboStatus === 'PAYMENT_FAILED' || temboStatus === 'FAILED') {
                    statusCode = 400;
                    description = response.data.reason || response.data.message || "Payment failed";
                    this.saveTransactionLog(transactionId, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, description, response.data);

                } else if (temboStatus === 'PAYMENT_REJECTED') {
                    statusCode = 400;
                    description = response.data.reason || response.data.message || "Payment rejected";
                    this.saveTransactionLog(transactionId, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, description, response.data);

                } else if (temboStatus === 'PENDING_ACK' || temboStatus === 'PENDING' || temboStatus === 'PROCESSING') {
                    statusCode = 202;
                    description = response.data.reason || response.data.message || "Payment pending";
                    this.saveTransactionLog(transactionId, "PENDING", Steps.THIRDPARTY_RESPONSE, 202, description, response.data);

                } else {
                    // Unknown status - treat as pending but log for investigation
                    statusCode = 202;
                    description = `Unknown status: ${temboStatus}`;
                    this.saveTransactionLog(transactionId, "UNKNOWN", Steps.THIRDPARTY_RESPONSE, 202, description, response.data);
                    console.warn(`⚠️ Unknown Tembo payout status: ${temboStatus} for transaction ${transactionId}`);
                }

            } else {
                // API call failed - treat as error
                statusCode = 500;
                description = response.message || "Failed to check transaction status";
                this.saveTransactionLog(transactionId, "ERROR", Steps.THIRDPARTY_RESPONSE, 500, description, response);
            }

            return this.mapPayoutResponse(statusCode, description, response.data);

        } catch (error: any) {
            console.error("❌ Error checking Tembo payout status:", error);
            const errorMessage = error?.message || "Transaction status check failed";
            this.saveTransactionLog(transactionId, "ERROR", Steps.THIRDPARTY_RESPONSE, 500, errorMessage, error);
            return this.mapPayoutResponse(500, errorMessage);
        }
    }
    mapPayoutResponse(statusCode: any, description: any, data: any = null, transStatus?: string): PollingResponse {
        // Map status codes to transStatus if not provided
        let mappedTransStatus = transStatus;
        if (!mappedTransStatus) {
            if (statusCode === 200) {
                mappedTransStatus = "SUCCESS";
            } else if (statusCode === 400) {
                mappedTransStatus = "FAILED";
            } else if (statusCode === 202) {
                mappedTransStatus = "PENDING";
            } else {
                mappedTransStatus = "ONHOLD";
            }
        }

        return {
            statusCode: statusCode,
            transStatus: mappedTransStatus,
            description: description,
            data: data
        }
    }

}

export default PollingPayoutsService;