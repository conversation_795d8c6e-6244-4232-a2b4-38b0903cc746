import StellarSdk, { Keypair } from "stellar-sdk";
import { Asset, Server } from 'stellar-sdk';

import { Request, Response } from "express";
import Model from './model'
require('dotenv').config()
import BaseModel from './base.model';

const SponserKey = process.env.SPONSOR_KEY
const url: any = process.env.HORIZON_URL
console.groupCollapsed("StellarService", { url })
const server = new StellarSdk.Server(url);
let network: any = StellarSdk.Networks.PUBLIC

let md: Model;

function getModelInstance(): Model {
    if (!md) {
        md = new Model();
    }
    return md;
}

if (url?.includes("testnet")) {
    network = StellarSdk.Networks.TESTNET
}
interface Recipient {
    publicKey: string;
    amount: string;
    asset_code: string;
    asset_issuer: string;
    senderSecretKey: string;
    creditPrivateKey: string
}

const feeAccounts: any = process.env.feeAccounts
export default class StellarService extends BaseModel {


    constructor() {
        super();

    }

    async delay(ms: number) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async getChannelAccount() {
        await this.callRawQuery("UPDATE `channel_accounts` SET `status` = 'free' WHERE `updated_at` < (NOW() - INTERVAL 1 MINUTE)");

        const maxWaitTime = 10000; // Maximum wait time of 10 seconds
        const checkInterval = 1000; // Check every 1 second
        const startTime = Date.now();

        let channelAccount: any = [];
        while (Date.now() - startTime < maxWaitTime) {
            channelAccount = await this.callRawQuery("SELECT * FROM channel_accounts WHERE status='free' LIMIT 1");
            if (channelAccount.length > 0) {
                const keyPairPostion = Math.floor(Math.random() * channelAccount.length);
                const pvKey = channelAccount[keyPairPostion]['private_key'];
            //    this.updateChannelAccount(pvKey, "inUse");
                return pvKey;
            }
            await this.delay(checkInterval);
        }
        return "";
    }



    async updateChannelAccount(private_key: any, status: string) {
        try {
            const PostData = {
                status
            };
            return await this.updateData('channel_accounts', `private_key='${private_key}'`, PostData);
        } catch (e) {
            return "";
        }
    }

    async getUserAccounts(clientId: string) {
        return await getModelInstance().getDecryptedApiKey(clientId)
    }

    async getPaymentOperations(user_id: string) {
        const keys: any = await this.getUserAccounts(user_id);
        if (keys == false) {
            return [];
        }
        const publicKey = keys.public_key;

        try {
            // Fetch payment operations using the public key
            const operations = await server.operations()
                .forAccount(publicKey)
                .order('desc')  // Orders results descending by operation ID
                .limit(20)      // Limits the number of operations retrieved
                .call();

            // Create an array to hold the operation data
            const paymentOperationsArray: any[] = [];

            // Loop through all operations and filter for 'payment' operations only
            for (const operation of operations.records) {
                if (operation.type === 'payment') {
                    // Fetch transaction to get memo
                    const transaction = await server.transactions().transaction(operation.transaction_hash).call();

                    // Determine if the operation is a debit (DR) or credit (CR)
                    const direction = operation.source_account === publicKey ? 'DR' : 'CR';

                    const paymentObj = {
                        id: operation.id,
                        type: operation.type,
                        created_at: operation.created_at, // Date of the operation
                        transaction_hash: operation.transaction_hash,
                        source_account: operation.source_account,
                        memo: transaction.memo || 'None', // Retrieve memo
                        amount: operation.amount,
                        asset_code: operation.asset_type === 'native' ? 'XLM' : operation.asset_code, // Set asset code to 'XLM' for native
                        direction, // DR for debit, CR for credit
                    };
                    paymentOperationsArray.push(paymentObj);
                }
            }

            return paymentOperationsArray;
        } catch (error) {
            console.error('Error fetching payment operations:', error);
            return [];
        }
    }



    async getAssetHolders(currency: string) {
        const assetCode = currency.toUpperCase();
        const assetCodeC = "c" + assetCode;
        const issuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC;

        if (!issuer) {
            throw new Error("STELLAR_PAYOUT_ISSUER_PUBLIC is not defined in environment variables");
        }

        console.log(`Fetching holders for ${assetCode} and ${assetCodeC} from issuer ${issuer}`);

        const holders: Record<string, { accountId: string; cBalance: string; balance: string }> = {};
        const assetsToQuery = [
            new Asset(assetCode, issuer),
            new Asset(assetCodeC, issuer),
        ];

        for (const asset of assetsToQuery) {
            let page = await server.accounts().forAsset(asset).limit(200).call();

            while (true) {
                for (const account of page.records) {
                    const balances = account.balances;
                    const cTrustline = balances.find(
                        (b: any) =>
                            b.asset_type !== "native" &&
                            b.asset_code === assetCodeC &&
                            b.asset_issuer === issuer
                    );
                    const trustline = balances.find(
                        (b: any) =>
                            b.asset_type !== "native" &&
                            b.asset_code === assetCode &&
                            b.asset_issuer === issuer
                    );

                    holders[account.account_id] = {
                        accountId: account.account_id,
                        cBalance: cTrustline ? cTrustline.balance : holders[account.account_id]?.cBalance || "0",
                        balance: trustline ? trustline.balance : holders[account.account_id]?.balance || "0"
                    };
                }

                if (page.records.length < 200 || !page.next) break;
                page = await page.next();
            }
        }

        const result = Object.values(holders);
        console.log(`Found ${result.length} unique holders`);
        return result;
    }


    async getAssetSupply(assetCode: string) {
        try {
            const issuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC;
            // Query Horizon for the asset record
            const assetRecords = await server
                .assets()
                .forCode(assetCode)
                .forIssuer(issuer)
                .limit(1)
                .call();

            if (assetRecords.records.length === 0) {
                throw new Error(`No asset found for code: ${assetCode} issuer: ${issuer}`);
            }

            // The first record should represent the given asset
            const assetRecord = assetRecords.records[0];
            console.log(`assetRecord`, assetRecord)

            // The "amount" field tells us the total number of units issued/outstanding
            const totalAmount = assetRecord.balances.authorized;
            // The "num_accounts" field gives the number of accounts that hold this asset
            const numberOfAccounts = assetRecord.num_accounts;

            return totalAmount;
            return {
                totalSupply: totalAmount,
                holderCount: numberOfAccounts,
            };
        } catch (error) {
            console.error('Error fetching asset supply:', error);
            return 0
        }
    }

    async getBalance(user_id: string, expectedCurrencies: string[] = ['UGX', 'KES']) {
        const keys: any = await this.getUserAccounts(user_id);
        if (!keys) {
            return [];
        }

        const public_key = keys.public_key;
        console.log("Fetching balances for:", public_key, user_id);

        let stellarBalances = [];
        try {
            // Attempt to load the Stellar account
            const recPbKey = await server.loadAccount(public_key);
            stellarBalances = recPbKey.balances;
        } catch (error: any) {
            console.error("Account not active or not found:", error.message);
            // Optionally, check for specific error codes (e.g., 404) to handle only account-not-found cases.
        }

        const bArray: any = [];

        // Process balances returned from Stellar (if any)
        if (stellarBalances && stellarBalances.length > 0) {
            stellarBalances.forEach((element: any) => {
                let code = element.asset_type === "native" ? "XLM" : element.asset_code;
                bArray.push({ code, balance: element.balance });
            });
        }

        // Ensure all expected currencies are included; if missing, add them with a zero balance
        expectedCurrencies.forEach(currency => {
            if (!bArray.find((bal: any) => bal.code === currency)) {
                bArray.push({ code: currency, balance: "0" });
            }
        });

        return bArray;
    }


    async getTotalCirculatingSupply(assetCode: string, issuer: string) {
        try {
            console.log(`SUPPLY_INFO`, { assetCode, issuer })
            // Fetch all accounts that hold this asset
            const accountsHoldingAsset = await server.assets()
                .forCode(assetCode)
                .forIssuer(issuer)
                .call();

            if (!accountsHoldingAsset.records || accountsHoldingAsset.records.length === 0) {
                return 0; // No accounts hold this asset
            }

            // Access the asset's total supply directly
            const totalSupply = accountsHoldingAsset.records[0].amount;
            console.log(`Total circulating supply for ${assetCode}: ${totalSupply}`);
            return totalSupply
        } catch (error) {
            console.error("Error fetching total circulating supply:", error);
            return 0;
        }
    }



    async getSingleBalance(user_id: string, currencyCode: string) {
        const keys: any = await this.getUserAccounts(user_id);
        if (!keys) {
            return null; // Return null or a default value if the user has no accounts
        }
        const public_key = keys.public_key;
        const recPbKey = await server.loadAccount(public_key);
        const balances = recPbKey.balances;
        console.log("balances", public_key, balances);

        // Find the balance for the specified currency
        const balance = balances.find((element: any) => {
            if (currencyCode === "XLM" && element.asset_type === "native") {
                return true;
            } else if (element.asset_code === currencyCode) {
                return true;
            }
            return false;
        });

        // Return the balance object if found, or null if not
        return balance ? { code: currencyCode, balance: parseFloat(balance.balance).toFixed(2) } : null;
    }


    async getUserWallet(user_id: string) {
        return await this.callQuerySafe(`SELECT * FROM stellar_wallets WHERE user_id = ? LIMIT 1`, [user_id]);
    }

 




    MakeResponse(code: number, message: string): any {
        const response: any = {
            response: code,
            message: message,
        };
        return response;
    }


    async makeSinglePayment(trans_id: string,senderSecretKey: string, receiverPublicKey: string, destinationPvKey: string, asset_code: string, asset_issuer: string, amount: string, memo: string, signersArray: any) {

        console.log("PaymentStep1===>", senderSecretKey, receiverPublicKey, destinationPvKey, asset_code, asset_issuer, amount, memo, signersArray)
        const SponseringAccount = StellarSdk.Keypair.fromSecret(SponserKey);



        /*
        const feeArray = JSON.parse(feeAccounts);
        const keyPairPostion = Math.floor(Math.random() * feeArray.length);
        const payerKey = feeArray[keyPairPostion];
*/

        const payerKey = await this.getChannelAccount();

        const sourceAccountKeyPair = StellarSdk.Keypair.fromSecret(payerKey);
        let destPvKey = '';
        amount = amount.toString();

        const issuerPv = await getModelInstance().GetIssuerAccount(asset_code, "private")
        const issuingKeys = StellarSdk.Keypair.fromSecret(issuerPv);
        let includeSigner = false;


        try {
            if (destinationPvKey != '') {
                destPvKey = StellarSdk.Keypair.fromSecret(destinationPvKey);
            }
        } catch (err) {
            this.updateChannelAccount(payerKey, "free");

            console.log("PaymentStep2", err)
            return "error"
        }

        let signers: any = [];

        let asset: any = null;
        let assetType = "native";
        console.log(`ISSUERS`, asset_code)

        if (asset_code !== undefined) {
            const issuer = asset_issuer;
            asset = new StellarSdk.Asset(asset_code, issuer);
            assetType = "alpha";
        }


        let exists = true;
        let hasTrustLine = true;
        this.saveTransactionLog(trans_id, "PENDING", { asset_code, asset_issuer, amount,memo  })

        try {

            if (assetType === "alpha") {
                const assetCode = asset.code;
                const assetIssuer = asset.issuer;
                if (assetIssuer == receiverPublicKey) {
                    hasTrustLine = true;

                } else {
                    const recPbKey = await server.loadAccount(receiverPublicKey);
                    hasTrustLine = recPbKey.balances.some((balance: { asset_code: any; asset_issuer: any; }) => {
                        return balance.asset_code === assetCode && balance.asset_issuer === assetIssuer;
                    });
                }

            }
        } catch (err) {
            console.log("PaymentStep3", err)
            exists = false;
            hasTrustLine = false;
        }

        try {

            const senderKeyPair: any = StellarSdk.Keypair.fromSecret(senderSecretKey);

            if (Array.isArray(signersArray)) {
                const signerInfo = signersArray
                signerInfo.forEach((account) => {
                    const sAccount: any = StellarSdk.Keypair.fromSecret(account);
                    signers.push(sAccount);
                });
            } else {
                signers.push(senderKeyPair);
            }

            const fees = 1000000; // Set the desired fee value

            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(sourceAccountKeyPair.publicKey()),
            ]);

            const transaction_builder = new StellarSdk.TransactionBuilder(distributionAccount, {
                fee: String(fees),
                networkPassphrase: network,
            });

            if (!exists || !hasTrustLine) {

                transaction_builder.addOperation(StellarSdk.Operation.beginSponsoringFutureReserves({
                    sponsoredId: receiverPublicKey,
                    source: SponseringAccount.publicKey()
                }));
                console.log("PaymentStep5", exists)
                if (!exists) {


                    transaction_builder.addOperation(StellarSdk.Operation.createAccount({
                        startingBalance: "0",
                        destination: receiverPublicKey,
                        source: SponseringAccount.publicKey()
                    }));

                }
                console.log("hasTrustLine", hasTrustLine)

                if (!hasTrustLine) {
                    transaction_builder.addOperation(StellarSdk.Operation.changeTrust({
                        asset: asset,
                        source: receiverPublicKey,
                    }));
                    includeSigner = true;
                    transaction_builder.addOperation(StellarSdk.Operation.allowTrust({
                        trustor: receiverPublicKey,
                        assetCode: asset_code,
                        authorize: true,
                        source: issuingKeys.publicKey()
                    }));

                }

                transaction_builder.addOperation(StellarSdk.Operation.endSponsoringFutureReserves({
                    source: receiverPublicKey,
                }));
            }
            transaction_builder.addOperation(StellarSdk.Operation.payment({
                destination: receiverPublicKey,
                asset: asset,
                amount: amount,
                source: senderKeyPair.publicKey(),
            }));

            transaction_builder.setTimeout(20);
            transaction_builder.addMemo(StellarSdk.Memo.text(memo));

            const transaction = transaction_builder.build();

            signers.push(sourceAccountKeyPair);

            if (!exists || !hasTrustLine) {
                console.log("")
                signers.push(destPvKey);
                signers.push(SponseringAccount);

            }

            console.log("==============START============")


            signers.forEach((signer: any) => {
                console.log("Signing with public key:", signer.publicKey());
                transaction.sign(signer);
            });

            const issuerPv = await getModelInstance().GetIssuerAccount(asset_code, "private")
            if (includeSigner && senderSecretKey != issuerPv) {
                transaction.sign(issuingKeys);
            }
            console.log("==============END============")


            try {
                const transactionResult = await server.submitTransaction(transaction);
                console.log("tx Sent", transactionResult)
                this.updateTransactionLog(trans_id, "SUCCESS", transactionResult);
                this.updateChannelAccount(payerKey, "free");

                if (transactionResult.successful) {

                    return this.MakeResponse(1, transactionResult.hash)
                } else {
                    return this.MakeResponse(101, "not processed")
                }
            } catch (e: any) {
                this.updateChannelAccount(payerKey, "free");

                if (e.code === 'ECONNABORTED' || e.message.includes('timeout')) {
                    return this.MakeResponse(504, "Gateway Timeout or Network Issue");
                }

                console.log("Error during transaction submission");

                let errorDetail = e.response?.data?.extras?.result_codes;
                let errorMessage = "Transaction could not be sent";
                console.log("errorMessage", errorDetail)

                if (errorDetail) {
                    const operationError = errorDetail.operations;
                    const transactionError = errorDetail.transaction;

                    if (operationError === "tx_insufficient_balance") {
                        errorMessage = "Insufficient XLM balance to process transaction";
                    } else if (operationError === "payment_underfunded") {
                        errorMessage = "Insufficient Balance on your account";
                    } else {
                        errorMessage += `: ${transactionError || operationError || 'Unknown'}`;
                    }
                }
                this.updateTransactionLog(trans_id, "FAILED", errorMessage);

                if (errorMessage == "Unknown") {
                    return this.MakeResponse(504, errorMessage);
                } else {
                    console.log(errorMessage);
                    return this.MakeResponse(203, errorMessage);
                }
            }


        } catch (err) {
            this.updateChannelAccount(payerKey, "free");
            console.log("txError", err)
            this.updateTransactionLog(trans_id, "FAILED", err);
            return this.MakeResponse(203, "transaction not set properly");
        }
    }

    async saveTransactionLog(trans_id: string, status: string, recipients: any) {
        try {
            for (const recipient of recipients) {
                const txObject = {
                    trans_id,
                    status,
                    data:JSON.stringify(recipient)
                }
                await this.insertData("chain_transactions_log", txObject);
            }
        } catch (error: any) {
            console.error("Error in saveStableCoinTransaction:", error);
            return false;
        }
    }

    async updateTransactionLog(trans_id: string, status: string, response: any) {

        try {
                const txObject = {
                    trans_id,
                    status,
                    data:JSON.stringify(response)
                }
                await this.updateData("chain_transactions_log", `trans_id='${trans_id}'`, txObject);
        } catch (error: any) {
            console.error("Error in updateTransactionLog:", error);
            return false;
        }
    }

    async makeBatchTransfers(trans_id: string, memo: string, recipients: Recipient[]) {
        const channelAccount = await this.getChannelAccount();
        try {
            this.saveTransactionLog(trans_id, "PENDING", recipients)

            console.log("RECEIVED==>", recipients)
            console.log("RECEIVED==>", SponserKey)
            const SponsoringAccount = StellarSdk.Keypair.fromSecret(SponserKey);
            console.log(`SponserKey===>1`, SponserKey)
            console.log(`SponserKey===>2`, SponserKey)
            console.log(`SponserKey===>3`, SponserKey)

            /*
            const feeArray = JSON.parse(feeAccounts);
            const keyPairPosition = Math.floor(Math.random() * feeArray.length);
            const SponserKey = feeArray[keyPairPosition];
*/
            const sourceAccountKeyPair = StellarSdk.Keypair.fromSecret(channelAccount);



            let signers: Array<any> = [];
            let includeSigner = false;




            const fees = 1000000;

            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(sourceAccountKeyPair.publicKey()),
            ]);

            const transaction_builder = new StellarSdk.TransactionBuilder(distributionAccount, {
                fee: String(fees),
                networkPassphrase: network,
            });
            // this.saveLog(memo, "", "", "");
            let p = 0;
            console.log("STELLAR===>1")

            for (let recipient of recipients) {
                console.log(`OPERATION========>${p}`, recipient)
                let receiverPublicKey = recipient.publicKey;
                p++;



                const asset_code = recipient.asset_code;
                const asset_issuer = recipient.asset_issuer;
                const creditPrivateKey = recipient.creditPrivateKey;
                const senderSecretKey = recipient.senderSecretKey;
                const issuerPv = await getModelInstance().GetIssuerAccount(asset_code, "private")
                console.log("issuerPv===>", issuerPv)

                const issuingKeys = StellarSdk.Keypair.fromSecret(issuerPv);

                const assetType = 'alpha';
                let amount = Number(recipient.amount).toFixed(7);
                amount = amount.toString();


                const senderKeyPair = StellarSdk.Keypair.fromSecret(senderSecretKey);


                signers = this.addSignerIfNotExists(signers, senderKeyPair);

                //signers.push(senderKeyPair);

                const asset = new StellarSdk.Asset(asset_code, asset_issuer);

                let exists = true;
                let hasTrustLine = true;

                try {
                    // await server.loadAccount(receiverPublicKey);
                    // if (assetType === "alpha") {
                    const assetCode = asset_code;
                    const assetIssuer = asset_issuer;

                    if (assetIssuer == receiverPublicKey) {
                        hasTrustLine = true;
                    } else {

                        const recPbKey = await server.loadAccount(receiverPublicKey);
                        hasTrustLine = recPbKey.balances.some((balance: { asset_code: string | undefined; asset_issuer: string | undefined; }) => {
                            return balance.asset_code === assetCode && balance.asset_issuer === assetIssuer;
                        });
                    }
                    //}
                } catch (err) {
                    console.log("STELLAR===>3", err)
                    exists = false;
                    hasTrustLine = false;
                }
                console.log("exists===>", exists)
                console.log("hasTrustLine===>", hasTrustLine)

                if (!exists || !hasTrustLine) {
                    const creditPvKeyPair = StellarSdk.Keypair.fromSecret(creditPrivateKey);
                    signers = this.addSignerIfNotExists(signers, creditPvKeyPair);
                    signers = this.addSignerIfNotExists(signers, SponsoringAccount);

                    //1. create account, 2. change trust,3. allow trust, 4.payment

                    transaction_builder.addOperation(StellarSdk.Operation.beginSponsoringFutureReserves({
                        sponsoredId: receiverPublicKey,
                        source: SponsoringAccount.publicKey()
                    }));

                    if (!exists) {
                        transaction_builder.addOperation(StellarSdk.Operation.createAccount({
                            startingBalance: "0",
                            destination: receiverPublicKey,
                            source: SponsoringAccount.publicKey()
                        }));
                    }

                    if (!hasTrustLine) {
                        transaction_builder.addOperation(StellarSdk.Operation.changeTrust({
                            asset: asset,
                            source: receiverPublicKey,
                        }));
                        includeSigner = true;
                        transaction_builder.addOperation(StellarSdk.Operation.allowTrust({
                            trustor: receiverPublicKey,
                            assetCode: asset_code,
                            authorize: true,
                            source: issuingKeys.publicKey()
                        }));
                    }

                    transaction_builder.addOperation(StellarSdk.Operation.endSponsoringFutureReserves({
                        source: receiverPublicKey,
                    }));
                }

                transaction_builder.addOperation(StellarSdk.Operation.payment({
                    destination: receiverPublicKey,
                    asset: asset,
                    amount: amount,
                    source: senderKeyPair.publicKey(),
                }));

                if (includeSigner) {
                    signers = this.addSignerIfNotExists(signers, issuingKeys);
                }
            }

            transaction_builder.setTimeout(20);
            transaction_builder.addMemo(StellarSdk.Memo.text(memo));
            const transaction = transaction_builder.build();

            //signers.push(sourceAccountKeyPair);
            signers = this.addSignerIfNotExists(signers, sourceAccountKeyPair);
            signers.forEach((signer: any) => {
                console.log("Signing with public key:", signer.publicKey());
                transaction.sign(signer);
            });

            try {
                const transactionResult = await server.submitTransaction(transaction);
                console.log("StellarResponse===>", transactionResult)
                this.updateChannelAccount(SponserKey, "free");

                if (transactionResult.successful) {
                    this.updateTransactionLog(trans_id, "SUCCESS", {hash:transactionResult.hash});
                    return this.MakeResponse(1, transactionResult.hash)
                } else {
                    this.updateTransactionLog(trans_id, "FAILED", {error:transactionResult.result_xdr});
                    return this.MakeResponse(101, "not processed")
                }
            } catch (e: any) {
                this.updateChannelAccount(SponserKey, "free");

                if (e.code === 'ECONNABORTED' || e.message.includes('timeout')) {
                    this.updateTransactionLog(trans_id, "TIMEOUT", {error:e.message});
                    return this.MakeResponse(504, "Gateway Timeout or Network Issue");
                }

                console.log("Error during transaction submission");

                let errorDetail = e.response?.data?.extras?.result_codes;
                let errorMessage = "stellar transaction error";
                console.log("errorMessage", errorDetail)
                if (errorDetail) {
                    const operationError = errorDetail.operations;
                    const transactionError = errorDetail.transaction;

                    if (operationError === "tx_insufficient_balance") {
                        errorMessage = "Insufficient XLM balance to process transaction";
                    } else if (operationError === "payment_underfunded") {
                        errorMessage = "Insufficient Balance on your account";
                    } else {
                        errorMessage += `: ${transactionError || operationError || 'Unknown'}`;
                    }
                }

                this.updateTransactionLog(trans_id, "FAILED", errorDetail);

                if (errorMessage == "Unknown") {
                    return this.MakeResponse(203, errorMessage);
                } else {
                    console.log(errorMessage);
                    return this.MakeResponse(203, errorMessage);
                }
            }


        } catch (err) {
            this.updateChannelAccount(SponserKey, "free");

            console.log("txError", err)
                this.updateTransactionLog(trans_id, "FAILED", err);
            return this.MakeResponse(203, "transaction not set properly");
        }
    }

    addSignerIfNotExists(signers: Array<any>, keyPair: Keypair) {
        if (!signers.some(signer => signer.publicKey() === keyPair.publicKey())) {
            signers.push(keyPair);
        }
        return signers;
    }

    createRecipient(
        publicKey: string,
        amount: string,
        asset_code?: string,
        asset_issuer?: string,
        destinationPvKey?: string
    ) {
        return {
            publicKey,
            amount,
            asset_code,
            asset_issuer,
            destinationPvKey,
        };
    }




    async changeTrustOperation(req: { body: { source: any; signers: any; Operation: any; asset_code: any; asset_issuer: any; }; }) {
        console.log("received RQ ", req);
        let limit: string = "************";
        const sourcePvKey = req.body.source;
        const sponserPvKey = SponserKey;

        const accounts = req.body.signers;
        const Operation = req.body.Operation;
        if (Operation === "REMOVE") {
            limit = "0";
        }

        const tokenToAdd = req.body.asset_code;
        const tokenToAddIssuer = req.body.asset_issuer;

        const SponseringAccount = StellarSdk.Keypair.fromSecret(sponserPvKey);
        const sourceAccount = StellarSdk.Keypair.fromSecret(sourcePvKey);

        const distributedCurrency = "100";

        const TokenToBeAdded = new StellarSdk.Asset(
            tokenToAdd,
            tokenToAddIssuer,
        );
        const fees = 1000000; // Set the desired fee value

        try {
            // Fetch the base fee and the account that will create our transaction
            const [
                {
                    max_fee: { mode: fee },
                },
                distributionAccount,
            ] = await Promise.all([
                server.feeStats(),
                server.loadAccount(SponseringAccount.publicKey()),
            ]);

            const changeTrustTx = new StellarSdk.TransactionBuilder(
                distributionAccount,
                {
                    fee: String(fees),
                    networkPassphrase: network,
                },
            )



            changeTrustTx.addOperation(
                StellarSdk.Operation.beginSponsoringFutureReserves({
                    sponsoredId: sourceAccount.publicKey(),
                }),
            )


            changeTrustTx.addOperation(
                StellarSdk.Operation.changeTrust({
                    asset: TokenToBeAdded,
                    source: sourceAccount.publicKey(),
                    limit: limit,
                }),
            )

            changeTrustTx.addOperation(
                StellarSdk.Operation.endSponsoringFutureReserves({
                    source: sourceAccount.publicKey(),
                }),
            )


            let transaction = changeTrustTx.setTimeout(100).build();


            const keyPairArray = JSON.parse(accounts);
            keyPairArray.forEach((keyPair: any) => {
                const keyPairObject = StellarSdk.Keypair.fromSecret(keyPair);
                transaction.sign(keyPairObject);
            });
            transaction.sign(SponseringAccount);


            const txResult = await server.submitTransaction(transaction);

            console.log(
                `Success! ${SponseringAccount.publicKey()
                } key pair ${distributedCurrency} and sponsored by ${SponseringAccount.publicKey()
                }`,
            );

            return {
                "status": "success",
                "data": txResult
            }

        } catch (e: any) {
            console.error("Oh no! Something went wrong." + e);
            return {
                "status": "error",
                "data": "failed"
            };
        }
    }

    generateKeyPair() {
        const NewAccountKeyPair = StellarSdk.Keypair.random();
        return {
            public_key: NewAccountKeyPair.publicKey(),
            private_key: NewAccountKeyPair.secret(),
        };
    }



    async initAccount() {
        const NewAccountKeyPair = StellarSdk.Keypair.random();
        return {
            public: NewAccountKeyPair.publicKey(),
            private: NewAccountKeyPair.secret(),
            status: "success",
            data: "",
        };
    }






}


